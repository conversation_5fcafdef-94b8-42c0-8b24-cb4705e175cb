using Newtonsoft.Json;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using System.Diagnostics;
using Newarch.Utilities.VideoProcessing.Models;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

// Configure request size limits
builder.WebHost.ConfigureKestrel(options =>
{
    options.Limits.MaxRequestBodySize = 500_000_000; // 500MB limit
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

app.UseHttpsRedirection();

app.MapPost("", async (HttpContext context, ILoggerFactory loggerFactory) =>
{
    var logger = loggerFactory.CreateLogger("EncodeVideo");
    logger.LogInformation("Video encoding API started.");

    // Read and deserialize request body
    string requestBody = await new StreamReader(context.Request.Body).ReadToEndAsync();

    if (string.IsNullOrWhiteSpace(requestBody))
    {
        context.Response.StatusCode = StatusCodes.Status400BadRequest;
        await context.Response.WriteAsync("Request body cannot be empty.");
        return;
    }

    VideoRequest? requestData;
    try
    {
        requestData = JsonConvert.DeserializeObject<VideoRequest>(requestBody);
    }
    catch (JsonException ex)
    {
        logger.LogWarning(ex, "Invalid JSON in request body");
        context.Response.StatusCode = StatusCodes.Status400BadRequest;
        await context.Response.WriteAsync("Invalid JSON format in request body.");
        return;
    }

    if (string.IsNullOrEmpty(requestData?.ConnectionString))
    {
        context.Response.StatusCode = StatusCodes.Status400BadRequest;
        await context.Response.WriteAsync("Please provide connectionString in the request body.");
        return;
    }

    if (string.IsNullOrEmpty(requestData?.ThumbUrl))
    {
        context.Response.StatusCode = StatusCodes.Status400BadRequest;
        await context.Response.WriteAsync("Please provide thumbUrl in the request body.");
        return;
    }

    if (string.IsNullOrEmpty(requestData?.SourceUrl) || string.IsNullOrEmpty(requestData?.DestUrl))
    {
        context.Response.StatusCode = StatusCodes.Status400BadRequest;
        await context.Response.WriteAsync("Please provide both sourceUrl and destUrl in the request body.");
        return;
    }

    try
    {
        // Blob storage connection
        var blobServiceClient = new BlobServiceClient(requestData.ConnectionString);
        var (srcContainerName, srcBlobName) = GetContainerAndBlobNames(requestData.SourceUrl);
        var (thumbContainerName, thumbBlobName) = GetContainerAndBlobNames(requestData.ThumbUrl);
        var (destContainerName, destBlobName) = GetContainerAndBlobNames(requestData.DestUrl);

        // Temporary file paths
        string tempVideoPath = Path.GetTempFileName();
        string compressedVideoPath = $"{tempVideoPath}_{Guid.NewGuid()}.mp4";
        string thumbnailPath = $"{tempVideoPath}_{Guid.NewGuid()}.jpg";

        try
        {

        // Download the source video
        logger.LogInformation("Downloading video from source blob...");
        var srcBlobContainerClient = blobServiceClient.GetBlobContainerClient(srcContainerName);
        var srcBlobClient = srcBlobContainerClient.GetBlobClient(srcBlobName);
        await srcBlobClient.DownloadToAsync(tempVideoPath);

        // FFmpeg path
        string ffmpegPath = Path.Combine(Directory.GetCurrentDirectory(), "ffmpeg/ffmpeg.exe");
        logger.LogInformation("FFmpeg path: {FFmpegPath}", ffmpegPath);
        if (!File.Exists(ffmpegPath))
        {
            throw new FileNotFoundException($"FFmpeg executable not found at {ffmpegPath}.");
        }

        // Compress video
        logger.LogInformation("Compressing video...");
        await RunFFmpeg(ffmpegPath, $"-i \"{tempVideoPath}\" -vf scale=-1:720 -c:v libx264 -preset fast -crf 23 -c:a aac -b:a 128k \"{compressedVideoPath}\"");

        // Generate thumbnail
        logger.LogInformation("Generating thumbnail...");
        await RunFFmpeg(ffmpegPath, $"-i \"{compressedVideoPath}\" -vf thumbnail -frames:v 1 \"{thumbnailPath}\"");

        // Upload compressed video
        logger.LogInformation("Uploading compressed video...");
        var destBlobContainerClient = blobServiceClient.GetBlobContainerClient(destContainerName);
        var destVideoBlobClient = destBlobContainerClient.GetBlobClient(destBlobName);
        await UploadFileToBlob(destVideoBlobClient, compressedVideoPath, "video/mp4");

        // Upload thumbnail
        logger.LogInformation("Uploading thumbnail...");
        var thumbBlobContainerClient = blobServiceClient.GetBlobContainerClient(thumbContainerName);
        // string thumbnailBlobName = Path.GetFileNameWithoutExtension(destBlobName) + "_thumb.jpg";
        var destThumbnailBlobClient = thumbBlobContainerClient.GetBlobClient(thumbBlobName);
        await UploadFileToBlob(destThumbnailBlobClient, thumbnailPath, "image/jpeg");
        

        // Return response
        var response = new
        {
            VideoUrl = destVideoBlobClient.Uri,
            ThumbUrl = destThumbnailBlobClient.Uri
        };

            context.Response.ContentType = "application/json";
            await context.Response.WriteAsync(JsonConvert.SerializeObject(response));
        }
        finally
        {
            // Clean up temporary files
            try
            {
                if (File.Exists(tempVideoPath)) File.Delete(tempVideoPath);
                if (File.Exists(compressedVideoPath)) File.Delete(compressedVideoPath);
                if (File.Exists(thumbnailPath)) File.Delete(thumbnailPath);
            }
            catch (Exception cleanupEx)
            {
                logger.LogWarning(cleanupEx, "Failed to clean up temporary files");
            }
        }
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Error processing video");
        context.Response.StatusCode = StatusCodes.Status500InternalServerError;
        await context.Response.WriteAsync("Error processing video.");
    }
});


async Task RunFFmpeg(string ffmpegPath, string arguments)
{
    using var process = new Process
    {
        StartInfo = new ProcessStartInfo
        {
            FileName = ffmpegPath,
            Arguments = arguments,
            RedirectStandardError = true,
            RedirectStandardOutput = true,
            UseShellExecute = false,
            CreateNoWindow = true
        }
    };

    process.Start();

    // Add timeout for FFmpeg operations (10 minutes)
    using var cts = new CancellationTokenSource(TimeSpan.FromMinutes(10));

    try
    {
        string errorOutput = await process.StandardError.ReadToEndAsync();
        await process.WaitForExitAsync(cts.Token);

        if (process.ExitCode != 0)
        {
            throw new Exception($"FFmpeg failed: {errorOutput}");
        }
    }
    catch (OperationCanceledException)
    {
        process.Kill();
        throw new TimeoutException("FFmpeg operation timed out after 10 minutes");
    }
}

async Task UploadFileToBlob(BlobClient blobClient, string filePath, string contentType)
{
    await using var fileStream = File.OpenRead(filePath);
    var headers = new BlobHttpHeaders { ContentType = contentType };
    await blobClient.UploadAsync(fileStream, headers);
}

(string ContainerName, string BlobName) GetContainerAndBlobNames(string blobUrl)
{
    if (string.IsNullOrWhiteSpace(blobUrl))
        throw new ArgumentException("Blob URL cannot be null or empty", nameof(blobUrl));

    if (!Uri.TryCreate(blobUrl, UriKind.Absolute, out var uri))
        throw new ArgumentException("Invalid blob URL format", nameof(blobUrl));

    var segments = uri.AbsolutePath.Trim('/').Split('/');
    if (segments.Length < 2)
        throw new ArgumentException("Blob URL must contain container and blob name", nameof(blobUrl));

    return (segments[0], string.Join("/", segments.Skip(1)));
}

app.Run();
